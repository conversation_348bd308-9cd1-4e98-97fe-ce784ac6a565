#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PYTHIA代币全面分析与报告发送器 - 完整整合版本
集成了数据分析、报告生成、HTML转图片和Telegram发送功能的完整解决方案
包含持续监控模式，自动处理HTML文件并发送到Telegram群组
"""

import os
import asyncio
import time
import requests
import json
import threading
import schedule
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from pathlib import Path
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import config
from config import PYTHIA_TOKEN_INFO, API_CONFIG, ANALYSIS_CONFIG, SEARCH_KEYWORDS, OUTPUT_CONFIG

class HeadlessHTMLConverter:
    """无头浏览器HTML转图片工具"""
    
    def __init__(self):
        self.output_dir = Path("images")
        self.output_dir.mkdir(exist_ok=True)
        self.last_process_time = None  # 记录上次处理时间
    
    def setup_driver(self, width=None, height=None):
        """设置无头Chrome浏览器"""
        chrome_options = Options()
        chrome_options.add_argument('--headless=new')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--disable-web-security')
        chrome_options.add_argument('--disable-extensions')
        chrome_options.add_argument('--hide-scrollbars')
        chrome_options.add_argument('--enable-javascript')  # 启用JavaScript以加载图表
        chrome_options.add_argument('--mute-audio')
        chrome_options.add_argument('--force-device-scale-factor=1')
        chrome_options.add_argument('--disable-infobars')
        chrome_options.add_argument('--disable-notifications')
        chrome_options.add_argument('--disable-popup-blocking')
        
        # 使用传入的尺寸或默认尺寸
        w = width or 1920
        h = height or 1080
        chrome_options.add_argument(f'--window-size={w},{h}')
        
        # 自动下载并设置ChromeDriver
        service = Service(ChromeDriverManager().install())
        
        driver = webdriver.Chrome(service=service, options=chrome_options)
        driver.set_window_size(w, h)
        
        return driver
    
    def wait_for_chart_loading(self, driver):
        """等待TradingView图表完全加载"""
        try:
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC
            from selenium.webdriver.common.by import By
            
            wait = WebDriverWait(driver, 30)
            
            # 1. 等待TradingView脚本加载
            print("⏳ 等待TradingView脚本...")
            driver.execute_script("""
                var checkTradingView = function() {
                    return typeof TradingView !== 'undefined';
                };
                
                var startTime = Date.now();
                var checkInterval = setInterval(function() {
                    if (checkTradingView() || Date.now() - startTime > 20000) {
                        clearInterval(checkInterval);
                        window.tradingViewLoaded = checkTradingView();
                    }
                }, 500);
            """)
            
            # 等待脚本检查完成
            WebDriverWait(driver, 25).until(
                lambda d: d.execute_script("return typeof window.tradingViewLoaded !== 'undefined'")
            )
            
            tv_loaded = driver.execute_script("return window.tradingViewLoaded")
            if tv_loaded:
                print("✅ TradingView脚本已加载")
            else:
                print("⚠️ TradingView脚本加载超时")
            
            # 2. 等待图表容器出现
            print("⏳ 等待图表容器...")
            try:
                wait.until(EC.presence_of_element_located((By.ID, "chart_container")))
                print("✅ 图表容器已找到")
            except:
                print("⚠️ 图表容器未找到，继续等待...")
            
            # 3. 等待图表内容渲染 - 检查iframe或canvas
            print("⏳ 等待图表内容渲染...")
            chart_rendered = False
            
            for attempt in range(20):  # 最多等待20秒
                try:
                    # 检查是否有图表内容
                    has_chart = driver.execute_script("""
                        var container = document.getElementById('chart_container');
                        if (!container) return false;
                        
                        // 检查iframe（TradingView常用）
                        var iframe = container.querySelector('iframe');
                        if (iframe) return true;
                        
                        // 检查canvas元素
                        var canvas = container.querySelector('canvas');
                        if (canvas) return true;
                        
                        // 检查是否有实际内容
                        return container.children.length > 0 && container.offsetHeight > 100;
                    """)
                    
                    if has_chart:
                        print("✅ 图表内容已渲染")
                        chart_rendered = True
                        break
                    
                    time.sleep(1)
                    
                except Exception as e:
                    print(f"⚠️ 图表检测异常: {e}")
                    break
            
            if not chart_rendered:
                print("⚠️ 图表渲染检测超时，继续处理...")
            
            # 4. 额外等待确保图表完全加载
            print("⏳ 最终等待图表稳定...")
            time.sleep(8)  # 给图表一些时间完全渲染
            print("✅ 图表加载等待完成")
            
        except Exception as e:
            print(f"⚠️ 图表加载等待失败: {e}")
            print("🔄 使用备用等待策略...")
            time.sleep(15)  # 备用等待时间
    
    def detect_content_size(self, driver):
        """智能检测页面内容的实际尺寸，去除多余空白"""
        try:
            # 更精确的内容尺寸检测
            dimensions = driver.execute_script("""
                // 获取所有可能的尺寸
                var body = document.body;
                var html = document.documentElement;
                
                // 获取实际内容区域
                var allElements = document.querySelectorAll('*');
                var maxRight = 0;
                var maxBottom = 0;
                var minLeft = window.innerWidth;
                var minTop = window.innerHeight;
                
                // 遍历所有元素找到实际内容边界
                for (var i = 0; i < allElements.length; i++) {
                    var el = allElements[i];
                    var rect = el.getBoundingClientRect();
                    
                    // 跳过不可见元素
                    if (rect.width === 0 || rect.height === 0) continue;
                    
                    maxRight = Math.max(maxRight, rect.right);
                    maxBottom = Math.max(maxBottom, rect.bottom);
                    minLeft = Math.min(minLeft, rect.left);
                    minTop = Math.min(minTop, rect.top);
                }
                
                // 计算实际内容尺寸
                var contentWidth = Math.max(maxRight - Math.max(0, minLeft), 1280);
                var contentHeight = Math.max(maxBottom - Math.max(0, minTop), 720);
                
                // 添加一些边距
                contentWidth = Math.min(contentWidth + 40, 1920);
                contentHeight = Math.min(contentHeight + 40, 1080);
                
                return {
                    width: Math.round(contentWidth),
                    height: Math.round(contentHeight),
                    bounds: {
                        left: minLeft,
                        top: minTop,
                        right: maxRight,
                        bottom: maxBottom
                    }
                };
            """)
            
            content_width = dimensions['width']
            content_height = dimensions['height']
            
            print(f"🔍 智能检测内容尺寸: {content_width}x{content_height}")
            print(f"📏 内容边界: left={dimensions['bounds']['left']:.1f}, right={dimensions['bounds']['right']:.1f}")
            
            return content_width, content_height
            
        except Exception as e:
            print(f"⚠️ 尺寸检测失败: {e}")
            return 1280, 720
